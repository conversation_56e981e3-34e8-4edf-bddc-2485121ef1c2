# 用户收件箱关联处理逻辑文档

## 一、业务流程说明

### 1. user_inbox_source_related 表
- 传入 source_id、source_type、user_uuid
- 查询 user_inbox_source_related 是否存在有效记录（未删除）
  - 若无，则新建一条记录
  - 若有，则跳过创建，直接进入下一步

### 2. user_inbox_video_related 表
- 以某一行 user_inbox_source_related 记录为基础，获取其关联的所有视频列表（[{ aweme_id, publish_time }]）
- 批量检查 user_inbox_video_related 是否已存在对应记录（user_uuid, video_id, source_type, is_deleted=False）
  - 若无，则新建记录
  - 若有，则跳过

---

## 二、流程图

```mermaid
flowchart TD
    A[开始] --> B{user_inbox_source_related是否存在?}
    B -- 否 --> C[新建user_inbox_source_related]
    B -- 是 --> D[获取视频列表]
    C --> D
    D --> E{user_inbox_video_related是否存在?}
    E -- 否 --> F[新建user_inbox_video_related]
    E -- 是 --> G[跳过]
    F --> H[下一个视频]
    G --> H
    H --> I{是否还有视频?}
    I -- 是 --> D
    I -- 否 --> J[结束]
```

---

## 三、时序图

```mermaid
sequenceDiagram
    participant Client
    participant InboxService
    participant DB

    Client->>InboxService: 提交 source_id, source_type, user_uuid
    InboxService->>DB: 查询 user_inbox_source_related
    alt 未找到
        InboxService->>DB: 新建 user_inbox_source_related
    end
    InboxService->>DB: 查询视频列表
    loop 视频列表
        InboxService->>DB: 查询 user_inbox_video_related
        alt 未找到
            InboxService->>DB: 新建 user_inbox_video_related
        end
    end
```

---

## 四、UML 类图

```mermaid
classDiagram
    class UserInboxSourceRelated {
        +uuid: str
        +user_uuid: str
        +source_id: str
        +source_type: str
        +create_time: datetime
        +update_time: datetime
        +is_deleted: bool
        +deleted_at: str
    }

    class UserInboxVideoRelated {
        +uuid: str
        +user_uuid: str
        +video_id: str
        +source_type: str
        +create_time: datetime
        +update_time: datetime
        +is_deleted: bool
        +deleted_at: str
    }

    UserInboxSourceRelated "1" -- "0..*" UserInboxVideoRelated : 关联
```

---

## 五、代码处理建议

- 优先用 ORM 查询和批量插入，减少字符串拼接
- 关联逻辑建议封装为 service 层方法，便于复用和测试
- 批量处理时注意事务和异常捕获，保证数据一致性

---

如需进一步细化某一环节或补充具体代码实现，可随时告知！
